/* CDK Drag and drop styles */
.service-row {
  transition: all 0.2s ease;
  position: relative;
  cursor: grab;
}

.service-row:active {
  cursor: grabbing;
}

/* CDK Drag states */
.service-row.cdk-drag-dragging {
  opacity: 0.6;
  transform: scale(0.98);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.services-container.cdk-drop-list-dragging .service-row:not(.cdk-drag-dragging) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Drag preview styles */
.drag-preview {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  overflow: hidden;
  width: var(--service-width, 300px);
  max-width: 400px;
}

/* Drag placeholder styles */
.drag-placeholder {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  opacity: 0.5;
}

/* Drag handle styles */
.drag-handle {
  cursor: grab;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.drag-handle:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

.drag-handle:active {
  cursor: grabbing;
}

/* Category column styles */
.category-column {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Ensure the services list takes up all available space */
.services-list {
  flex: 1;
  overflow-y: auto;
  min-height: 200px;
}
