import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { SuperCategoryRes } from '../../../model/response/super-category.model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { TranslateModule } from '@ngx-translate/core';
import { ServiceLabelComponent } from '../../common/service-label/service-label.component';
import { SocialIconComponent } from '../../common/social-icon/social-icon.component';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { Router } from '@angular/router';
import { IconsModule } from '../../../icons/icons.module';
import { CdkDragDrop, moveItemInArray, CdkDrag, CdkDropList, CdkDragHandle, CdkDragPreview, CdkDragPlaceholder, CdkDragStart } from '@angular/cdk/drag-drop';

// Extended interface for categories with platform info
interface ExtendedCategoryRes extends SuperCategoryRes {
  platformIcon?: string;
  isAllPlatforms?: boolean;
  isAllCategories?: boolean;
}

@Component({
  selector: 'app-drag-drop-services',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    TranslateModule,
    ServiceLabelComponent,
    SocialIconComponent,
    CdkDrag,
    CdkDropList,
    CdkDragHandle,
    CdkDragPreview,
    CdkDragPlaceholder
  ],
  templateUrl: './drag-drop-services.component.html',
  styleUrls: ['./drag-drop-services.component.css']
})
export class DragDropServicesComponent implements OnInit {
  platforms: SuperPlatformRes[] = [];
  categories: ExtendedCategoryRes[] = [];
  servicesByCategory: { [categoryId: string]: SuperGeneralSvRes[] } = {};
  loading = true;



  constructor(
    private adminService: AdminServiceService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.loading = true;
    this.adminService.getPlatformsWithServices().subscribe({
      next: (platforms) => {
        this.platforms = platforms;

        // Extract all categories
        const allCategories: ExtendedCategoryRes[] = [];
        platforms.forEach(platform => {
          // Sort categories by sort field before processing them
          const sortedCategories = [...platform.categories].sort((a, b) => a.sort - b.sort);

          sortedCategories.forEach(category => {
            // Add platform info to category for display purposes
            const categoryWithPlatform: ExtendedCategoryRes = {
              ...category,
              platformName: platform.name,
              platformIcon: platform.icon
            };
            allCategories.push(categoryWithPlatform);
          });
        });
        this.categories = allCategories.filter(category => !category.hide);

        // Subscribe to services by category
        this.adminService.servicesByCategory$.subscribe(servicesByCategory => {
          this.servicesByCategory = servicesByCategory;
          this.loading = false;
        });
      },
      error: (error) => {
        console.error('Error loading platforms:', error);
        this.loading = false;
      }
    });
  }

  // Map service to GService format for display

  // CDK Drag and drop event handlers
  onServiceDrop(event: CdkDragDrop<SuperGeneralSvRes[]>, categoryId: string): void {
    if (event.previousIndex === event.currentIndex) {
      return; // No change in position
    }

    console.log(`Reordering services in category ${categoryId} from index ${event.previousIndex} to ${event.currentIndex}`);

    // Update the local array first
    moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);

    // Call the reorder method
    this.handleServiceDrop(categoryId, categoryId, event.previousIndex, event.currentIndex);
  }

  onServiceDragStarted(event: CdkDragStart): void {
    // Get the width of the original element
    const element = event.source.element.nativeElement;
    const width = element.offsetWidth;

    // Set the CSS variable for the drag preview width
    document.documentElement.style.setProperty('--service-width', `${width}px`);
  }

  // Handle the actual reordering of services
  handleServiceDrop(sourceCategoryId: string, targetCategoryId: string, sourceIndex: number, targetIndex: number): void {
    console.log(`Moving service from category ${sourceCategoryId}, index ${sourceIndex} to category ${targetCategoryId}, index ${targetIndex}`);

    // If source and target categories are the same, reorder within the category
    if (sourceCategoryId === targetCategoryId) {
      // Adjust the target index if needed
      // When moving an item down, the target index needs to be adjusted because
      // the item was already removed from the array
      const adjustedTargetIndex = targetIndex > sourceIndex ? targetIndex - 1 : targetIndex;

      // Use the AdminServiceService to reorder the service
      this.adminService.reorderServiceInCategory(sourceCategoryId, sourceIndex, adjustedTargetIndex);
    } else {
      // If source and target categories are different, move the service to the new category
      this.adminService.moveServiceToCategory(sourceCategoryId, targetCategoryId, sourceIndex, targetIndex);
    }

    console.log('Service moved successfully');
  }

  // Navigate back to the main services page
  navigateToServices(): void {
    this.router.navigate(['/panel/services']);
  }
}
